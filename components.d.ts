/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TAside: typeof import('tdesign-vue-next')['Aside']
    TAvatar: typeof import('tdesign-vue-next')['Avatar']
    TButton: typeof import('tdesign-vue-next')['Button']
    TContent: typeof import('tdesign-vue-next')['Content']
    TDivider: typeof import('tdesign-vue-next')['Divider']
    TDrawer: typeof import('tdesign-vue-next')['Drawer']
    TEmpty: typeof import('tdesign-vue-next')['Empty']
    THeader: typeof import('tdesign-vue-next')['Header']
    THeadMenu: typeof import('tdesign-vue-next')['HeadMenu']
    TIcon: typeof import('tdesign-vue-next')['Icon']
    TLink: typeof import('tdesign-vue-next')['Link']
    TMenu: typeof import('tdesign-vue-next')['Menu']
    TMenuItem: typeof import('tdesign-vue-next')['MenuItem']
  }
}
