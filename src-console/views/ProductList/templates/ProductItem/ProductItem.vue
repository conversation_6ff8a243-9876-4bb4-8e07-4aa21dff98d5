<script setup lang="ts">
import { CardCommon } from '@src-console/components'
import type { ApplicationListGetRespVo } from '@src-common/api/userApi.ts'
defineProps<{
  data: ApplicationListGetRespVo
}>();
</script>

<template>
  <CardCommon>
    <div class="flex justify-between mt-2">
      <div class="flex gap-4">
        <!--  产品 logo  -->
        <div class="w-12 product-logo">
          <img class="w-full h-auto"
               :src="data.appIcon"
               alt="logo">
        </div>
        <!--  产品信息  -->
        <div>
          <div class="font-bold">{{ data.appName }}</div>
          <div class="text-size-xs text-gray-500">{{ data.appDesc }}</div>
        </div>
      </div>
      <div>
        <t-button variant="outline">详情</t-button>
      </div>
    </div>
  </CardCommon>
</template>

<style scoped lang="less">

</style>
