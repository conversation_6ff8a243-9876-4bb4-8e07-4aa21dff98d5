<script setup lang="ts">
import { onMounted, ref } from 'vue'
import type { ProductLinkDrawerInstance } from '@src-console/views/ProductList/templates'
import { CardCommon } from '@src-console/components'
import { type ApplicationListGetRespVo, fetchUserBindable } from '@src-common/api'

const visible = ref(false);

function show() {
  visible.value = true;
}
defineExpose<ProductLinkDrawerInstance>({
  show,
});

function handleLinkProduct(product: ApplicationListGetRespVo) {
  console.log('handleLinkProduct');
  window.open(`utools://${product.appName}/bindAccount?bindId=123`)
}
const productBindableList = ref<ApplicationListGetRespVo[]>([]);
onMounted(async () => {
  productBindableList.value = await fetchUserBindable();
})
</script>

<template>
  <t-drawer v-model:visible="visible"
            size="450px"
            :footer="false"
            destroy-on-close>
    <div class="grid gap-2">
      <CardCommon v-for="product in productBindableList"
                  :key="product.appId"
                  border-color="#bfbfbf"
                  @click="handleLinkProduct(product)">
        <div class="flex gap-4">
          <!--  产品 logo  -->
          <div class="w-12 product-logo">
            <img class="w-full h-auto"
                 :src="product.appIcon"
                 alt="logo" />
          </div>
          <!--  产品信息  -->
          <div>
            <div class="font-bold">{{ product.appName }}</div>
            <div class="text-size-xs text-gray-500">{{ product.appDesc }}</div>
          </div>
        </div>
      </CardCommon>
    </div>
  </t-drawer>
</template>

<style lang="less" scoped>
</style>
