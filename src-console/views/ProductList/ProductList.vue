<script setup lang="ts">
import { CardGroup } from '@src-console/components'
import { ProductLinkDrawer } from './templates'
import type { ProductLinkDrawerInstance } from './templates'
import { onMounted, ref, useTemplateRef } from 'vue'
import { isDesktop } from '@src-common/utils'
import { ProductItem } from './templates';
import { type ApplicationListGetRespVo, fetchUserAppList } from '@src-common/api'

const productLinkDrawerRef = useTemplateRef<ProductLinkDrawerInstance>('productLinkDrawerRef');
const listData = ref<ApplicationListGetRespVo[]>([]);
async function refreshData() {
  listData.value = await fetchUserAppList()
}
onMounted(() => {
  refreshData();
})
</script>

<template>
  <ProductLinkDrawer v-if="isDesktop()"
                     ref="productLinkDrawerRef" />
  <div>
    <CardGroup title="产品列表"
               border>
      <template #extend>
        <div>
          <t-button @click="productLinkDrawerRef?.show()">
            <template #icon>
              <t-icon class="i-u-linkPlus"></t-icon>
            </template>
            绑定产品
          </t-button>
        </div>
      </template>
      <ProductItem v-for="data in listData"
                   :key="data.appId"
                   :data="data">
      </ProductItem>
    </CardGroup>
  </div>
</template>

<style scoped>

</style>
