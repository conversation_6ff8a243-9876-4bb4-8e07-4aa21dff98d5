<script setup lang="ts">
import { ref } from 'vue'
import { CardGroup, CardCommon } from '@src-console/components'

const stats = ref([
  { title: '积分', value: '12,345', color: '#2463EB'},
  { title: '使用产品数', value: '8,901', color: '#9334E9' },
  { title: '注册天数', value: '¥234,567',  color: '#17A34A' },
  { title: '最近登录时间', value: '1,234', color: '#EA580B' }
]);
</script>

<template>
  <div class="dashboard-container">
    <CardGroup bor>
      <div class="stats-grid">
        <CardCommon
          v-for="stat in stats"
          :key="stat.title"
          :border-color="stat.color"
          :title="stat.title"
          size="medium"
          :clickable="true"
          class="stat-card">
          <div class="flex justify-end">
            <div class="stat-value" :style="{color: stat.color}">{{ stat.value }}</div>
          </div>
        </CardCommon>
      </div>
    </CardGroup>
    <CardGroup title="动态"
               icon="i-u-announce"
               border>
      <div class="min-h-[28vh]">
        <div class="w-full min-h-[28vh] flex justify-center items-center">
          <div style="color: var(--td-font-gray-4)">
            暂无动态
          </div>
        </div>
      </div>
    </CardGroup>
  </div>
</template>

<style scoped>
.dashboard-container {
  @apply  grid gap-4;
}
.stats-grid {
  @apply grid grid-cols-1 mobile:grid-cols-2 lg:grid-cols-4 gap-4;
}

.stat-card {
  @apply transition-all duration-200;
}

.stat-value {
  @apply text-2xl text-gray-900;
  font-weight: 1000;
}
</style>
