<script setup lang="ts">
import { CardCommon } from '@src-console/components'
import { SettingItem, SettingDivision } from '@xiaou66/u-web-ui';
import { onMounted, ref } from 'vue'
import { fetchUserInfo } from '@src-common/api'
import type { UserInfoRespVo, SocialUserInfoRespVo } from '@src-common/api'
import { keyBy } from 'es-toolkit'
import { MessagePlugin } from 'tdesign-vue-next'

function handleAccountId() {
  console.log('handleAccountId Click')
}
const userInfo = ref<UserInfoRespVo>();

const socialType2Info = ref<Record<number, SocialUserInfoRespVo>>({})
onMounted(async () => {
  userInfo.value = await fetchUserInfo();
  console.log(userInfo.value)
  socialType2Info.value = keyBy(userInfo.value.socialInfoList || [], (item) => item.type);
})
const socialTypeList = [
  {
    type: 31,
    title: '微信',
    desc: '绑定后可以使用微信登录',
    icon: 'i-u-wechat',
    bind() {
      MessagePlugin.info("暂时不提供绑定...")
    }
  },
];

</script>

<template>
  <div v-if="userInfo" class="grid gap-2">
    <CardCommon title="个人信息">
      <div>
        <SettingItem title="账号ID" @click="handleAccountId">
          {{userInfo.shortId}}
        </SettingItem>
        <SettingDivision />
        <SettingItem title="积分">
          <div>{{ userInfo.integral }}</div>
        </SettingItem>
        <SettingItem title="账号注册时间">
          <div>{{ userInfo.createTime }}</div>
        </SettingItem>
      </div>
    </CardCommon>
    <CardCommon title="社区账号">
      <SettingItem v-for="socialType in socialTypeList"
                   :key="socialType.type"
                   :title="socialType.title"
                   :desc="socialType.desc">
        <t-button v-if="!Object.keys(socialType2Info).includes(socialType.type.toString())"
                  theme="success"
                  @click.stop="socialType.bind()">
          <template #icon>
            <t-icon :class="socialType.icon"></t-icon>
          </template>
          绑定{{socialType.title}}
        </t-button>
        <div v-else class="flex gap-2 items-center">
          <div>{{ socialType2Info[socialType.type]?.nickname }}</div>
          <t-avatar
            shape="round"
            :image="socialType2Info[socialType.type]?.avatar">
          </t-avatar>
        </div>
      </SettingItem>
    </CardCommon>
  </div>
</template>

<style scoped>

</style>
