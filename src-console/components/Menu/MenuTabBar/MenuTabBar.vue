<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { type RouteRecord, useRoute, useRouter } from 'vue-router'



const router = useRouter()
const route = useRoute();
const menuRouterList = ref<RouteRecord[]>([])
onMounted(() => {
  menuRouterList.value = router.getRoutes().filter((item) => item.meta.menu);
})
async function go(item: RouteRecord) {
  await router.replace(item.path)
  console.log('route.name', route.name)
}


</script>

<template>
  <TmTabBar theme="tag" :split="false">
    <TmTabItem v-for="(menuItem) in menuRouterList"
                    :key="menuItem.name"
                    :value="menuItem.name as string"
                    @click="go(menuItem)">
      <template #icon>
        <t-icon :class="menuItem.meta.icon"></t-icon>
      </template>

    </TmTabItem>
  </TmTabBar>
</template>

<style scoped lang="less">

</style>
