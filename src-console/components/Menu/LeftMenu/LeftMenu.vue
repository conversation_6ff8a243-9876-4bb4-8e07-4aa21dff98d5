<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { type RouteRecord, useRoute, useRouter } from 'vue-router'


const collapsed = ref(document.documentElement.clientWidth <= 800)
function changeCollapsed() {
  collapsed.value = !collapsed.value
}
const router = useRouter()
const route = useRoute();
const menuRouterList = ref<RouteRecord[]>([])
onMounted(() => {
  menuRouterList.value = router.getRoutes().filter((item) => item.meta.menu);
})
function go(item: RouteRecord) {
  router.push(item.path)
}
</script>

<template>
  <t-menu theme="light" :value="route.name"
          :collapsed="collapsed"
          :width="['180px', '60px']">
    <template #operations>
      <t-button variant="text" shape="square"
                @click="changeCollapsed">
        <template #icon><t-icon name="view-list" /></template>
      </t-button>
    </template>
    <t-menu-item v-for="(menuItem) in menuRouterList"
                 :key="menuItem.name"
                 :value="menuItem.name" @click=go(menuItem)>
      <template #icon>
        <t-icon :class="menuItem.meta.icon"></t-icon>
      </template>
      {{menuItem.meta.title}}
    </t-menu-item>
  </t-menu>
</template>

<style scoped></style>
