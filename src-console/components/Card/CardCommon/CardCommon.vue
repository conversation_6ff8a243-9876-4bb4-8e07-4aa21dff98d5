<script setup lang="ts">
import { computed, useAttrs } from 'vue'
import Color from 'color'

interface Props {
  /** 卡片标题 */
  title?: string
  /** 卡片副标题 */
  subtitle?: string
  /** 卡片变体 */
  variant?: 'default' | 'outlined' | 'elevated' | 'filled'
  /** 卡片大小 */
  size?: 'small' | 'medium' | 'large'
  /** 是否可悬停 */
  hoverable?: boolean
  /** 是否可点击 */
  clickable?: boolean
  /** 是否显示分割线 */
  divider?: boolean
  /** 自定义类名 */
  class?: string
  /** 边框颜色 */
  borderColor?: string
  /** 边框透明度 (0-1) */
  borderOpacity?: number
  gradient?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'medium',
  hoverable: true,
  clickable: false,
  divider: false,
  borderColor: 'transparent',
  borderOpacity: 0.2,
  gradient: false,
})

const attrs = useAttrs()
// 检测是否绑定了 click 事件
const hasClick = computed(() => {
  return 'onClick' in attrs || 'onClickCapture' in attrs
});

// 计算边框颜色的 RGBA 值
const borderColorWithOpacity = computed(() => {
  if (props.borderColor === 'transparent') {
    return 'transparent';
  }
  return Color(props.borderColor).alpha(props.borderOpacity).toString();
})
const borderColorWithGradient1 = computed(() => {
  return Color(props.borderColor).alpha(0.1).toString();
})
const borderColorWithGradient2 = computed(() => {
  return Color(props.borderColor).alpha(0.02).toString();
})

// 计算卡片的CSS类
const cardClasses = computed(() => {
  const classes = ['common-card']

  // 变体样式
  classes.push(`common-card--${props.variant}`)

  // 大小样式
  classes.push(`common-card--${props.size}`)

  // 交互样式
  if (props.hoverable) {
    classes.push('common-card--hoverable')
  }

  if (hasClick.value) {
    classes.push('common-card__clickable')
  }

  if (props.gradient) {
    classes.push('common-card__gradient');
  }
  return classes
})


</script>

<template>
  <div
    :class="[cardClasses, props.class]"
    :role="clickable ? 'button' : undefined"
    :tabindex="clickable ? 0 : undefined"
    v-on="{
      ...(hasClick && { click: $attrs.onClick || $attrs.onClickCapture })
    }"
  >
    <!-- 卡片头部 -->
    <div v-if="title || subtitle || $slots.header" class="common-card__header">
      <slot name="header">
        <div v-if="title || subtitle" class="common-card__title-section">
          <h3 v-if="title" class="common-card__title">{{ title }}</h3>
          <p v-if="subtitle" class="common-card__subtitle">{{ subtitle }}</p>
        </div>
      </slot>
      <div v-if="$slots.actions" class="common-card__actions">
        <slot name="actions" />
      </div>
    </div>

    <!-- 分割线 -->
    <div v-if="divider && (title || subtitle || $slots.header)" class="common-card__divider" />

    <!-- 卡片内容 -->
    <div class="common-card__content">
      <slot />
    </div>

    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="common-card__footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.common-card {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1.5px solid v-bind(borderColorWithOpacity);
  // 默认变体
  &--default {
    //
  }

  &:hover {
    box-shadow: var(--u-shadow-sm);
  }

  // 大小变体
  &--small {
    @apply p-3;
  }

  &--medium {
    @apply p-4;
  }

  &--large {
    @apply p-6;
  }
}


.common-card__header {
  @apply flex items-start justify-between mb-3;
}

.common-card__title-section {
  @apply flex-1;
}

.common-card__title {
  @apply text-lg font-semibold text-gray-900 mb-1;
  color: var(--td-text-color-primary);
  margin: 0;
  line-height: 1.4;
}

.common-card__subtitle {
  @apply text-sm text-gray-600;
  color: var(--td-text-color-secondary);
  margin: 0;
  line-height: 1.4;
}

.common-card__gradient {
  background: linear-gradient(
    -200deg,
    v-bind(borderColorWithGradient1) 0%,
    v-bind(borderColorWithGradient2) 80%,
    var(--td-bg-color-container) 100%
  );
}

.common-card__actions {
  @apply flex items-center gap-2 ml-4;
}

.common-card__divider {
  height: 1px;
  background: var(--td-border-level-1-color);
  margin: 0 0 1rem 0;
}

.common-card__content {
  @apply flex-1;
}

.common-card__footer {
  @apply mt-4 pt-3;
  border-top: 1px solid var(--td-border-level-1-color);
}
.common-card__clickable {
  @apply cursor-pointer;
  transition: border 280ms ease;
  &:hover {
    border: 1.5px solid var(--td-brand-color);
  }
}
</style>
