<script setup lang="ts">
withDefaults(defineProps<{
  icon?: string;
  title?: string;
  description?: string;
  border?: boolean;
  borderColor?: string;
}>(), {
  borderColor: 'var(--td-border-level-1-color)',
});
// @unocss-include
</script>

<template>
  <div class="group-card">
    <div :class="{ 'group-card-header--border': border }">
      <slot v-if="$slots.title || title" name="title">
        <div class="flex justify-between group-card-header">
          <div class="flex items-center mb-2 gap-2">
            <div v-if="icon"
                 :class="icon"></div>
            <div class="group-card-title">
              {{ title }}
              <div></div>
            </div>
          </div>
          <slot v-if="$slots.extend" name="extend" />
        </div>
      </slot>
      <slot v-if="$slots.description || description"
            name="description">
        <p class="group-card-description">{{description || '13231231'}}</p>
      </slot>
    </div>
    <slot name="default"></slot>
  </div>
</template>

<style lang="less" scoped>
.group-card {
  --at-apply: p-4 rounded-lg;
  background: var(--td-bg-color-container);
  .group-card-title {
    --at-apply: text-lg font-bold text-gray-900;
    color: var(--td-text-color-primary);
  }

}
.group-card-header {
  --at-apply: px-1;
  &--border {
    --at-apply: pb-2;
    border-bottom: 1px solid v-bind(borderColor);
  }
}
.group-card-description {
  --at-apply: text-gray-600;
  color: var(--td-text-color-secondary);
}
</style>
