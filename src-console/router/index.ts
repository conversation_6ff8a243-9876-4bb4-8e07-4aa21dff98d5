import { createRouter, createWebHistory } from 'vue-router'

// @unocss-include
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL + "console"),
  routes: [
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('@src-console/views/Dashboard'),
      meta: {
        menu: true,
        icon: 'i-u-dashboard',
        title: '概括看板',
      }
    },
    {
      path: '/productList',
      name: 'productList',
      component: () => import('@src-console/views/ProductList'),
      meta: {
        menu: true,
        icon: 'i-u-app',
        title: '产品列表',
      }
    },
    {
      path: '/integralExchange',
      name: 'integralExchange',
      component: () => import('@src-console/views/IntegralExchange'),
      meta: {
        menu: true,
        icon: 'i-u-exchangeLine',
        title: '积分兑换',
      }
    },
    {
      path: '/userInfo',
      name: 'userInfo',
      component: () => import('@src-console/views/UserInfo'),
      meta: {
        menu: true,
        icon: 'i-u-users',
        title: '个人中心',
      }
    }
  ],
})

export default router
