/**
 * 判断当前设备是否为移动设备
 */
export function isMobile(): boolean {
  const deviceInfo = getDeviceInfo();
  if ('utools' in window) {
    // utools 环境
    return false;
  }
  return getDeviceInfo().isMobile || deviceInfo.type === 'mobile';
}

export function isDesktop() {
  return !isMobile();
}

/**
 * 获取设备信息
 */
export function getDeviceInfo() {
  const userAgent = navigator.userAgent.toLowerCase();
  const width = window.innerWidth;
  const isTouch = 'ontouchstart' in window;

  return {
    type: width <= 768 ? 'mobile' : width <= 1024 ? 'tablet' : 'desktop',
    isMobile: /mobile|android|iphone/i.test(userAgent),
    isTablet: /tablet|ipad/i.test(userAgent),
    isTouch: isTouch,
    width: width,
    height: window.innerHeight,
    userAgent: userAgent
  };
}
