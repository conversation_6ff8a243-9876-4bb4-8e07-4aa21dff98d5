import { createFetch } from 'ofetch';
import type { FetchOptions, FetchResponse, ofetch, FetchRequest } from 'ofetch';
import {MessagePlugin} from 'tdesign-vue-next'
const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
}

export interface IResponse<T> {
  code: number;
  data: T;
  msg: string;
}

export class ApiError extends Error {
  code: number;
  msg: string;
  constructor(code: number, msg: string) {
    super(msg)
    this.code = code;
    this.msg = msg;
  }
}
function isJson(response: FetchResponse<any>): boolean {
  // 方法1: 检查 Content-Type 头部
  const contentType = response.headers.get('content-type');
  const isJsonByHeader = contentType && contentType.includes('application/json');
  if (isJsonByHeader) {
    return true;
  }
  return response._data && typeof response._data === 'object';
}
const service = createFetch({
  defaults: {
    baseURL: BASE_URL,
    timeout: 5000,
    async onRequest({ request, options }) {
      options.headers.set('u-token', 'eM_hJxr0sRcgYKqI8_ZqZ1X0kY66hhLli8__')
    },
    async onResponse({ request, response, options }) {
      if (isJson(response)) {
        if (response._data.code !== 0) {
          throw new ApiError(response._data.code, response._data.msg);
        }
      }
    },
    async onRequestError({ request, options, error }) {
      // Log error
      console.log("[fetch request error]", request, error);
    },
  }
})
export function request<T>(request: FetchRequest, options?: FetchOptions<any>): Promise<T> {
  return new Promise((resolve, reject) => {
    service(request, options)
      .then(res => {
        resolve(res.data);
      })
      .catch(err => {
        if (err instanceof ApiError) {
          if (err.code <= 999) {
            if (err.code === 401) {
              MessagePlugin.warning('登录失效, 请重新登录');
              setTimeout(() => {
                window.location.href = '/login';
              }, 200);
            } else {
              MessagePlugin.error(err.msg);
            }
          }
        }
        reject(err);
      });
  })

}
