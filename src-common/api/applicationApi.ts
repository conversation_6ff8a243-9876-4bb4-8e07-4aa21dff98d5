import { request } from './request.ts'


/**
 * ApplicationListGetRespVo
 */
export interface ApplicationListGetRespVo {
  /**
   * 应用描述
   */
  appDesc?: string;
  /**
   * 应用图标
   */
  appIcon?: string;
  /**
   * 应用id
   */
  appId?: string;
  /**
   * 应用名称
   */
  appName?: string;
  [property: string]: any;
}

/**
 * 当前用户使用应用列表
 */
export function fetchUserAppList() {
  return request<ApplicationListGetRespVo[]>('/app/application/user/getList');
}

/**
 * 获取可绑定的用户应用
 */
export async function fetchUserBindable(): Promise<ApplicationListGetRespVo[]> {
  return await request<ApplicationListGetRespVo[]>('/app/application/user/getBindable');
}
