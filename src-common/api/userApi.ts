import { request } from './request.ts'


/**
 * UserInfoRespVo
 */
export interface UserInfoRespVo {
  /**
   * 头像
   */
  avatar?: string;
  /**
   * 注册时间
   */
  createTime?: string;
  /**
   * 积分
   */
  integral?: string;
  /**
   * 短 id
   */
  shortId?: string;
  /**
   * 社区账号
   */
  socialInfoList?: SocialUserInfoRespVo[];
  /**
   * 用户名称
   */
  username?: string;
  [property: string]: any;
}

/**
 * SocialUserInfoRespVo
 */
export interface SocialUserInfoRespVo {
  /**
   * 第三方账号头像
   */
  avatar?: string;
  /**
   * 第三方账号昵称
   */
  nickname?: string;
  /**
   * 社区类型
   */
  type: number;
  [property: string]: any;
}

/**
 * 用户信息
 */
export async function fetchUserInfo(): Promise<UserInfoRespVo> {
  return await request<UserInfoRespVo>('/app/user/info');
}
