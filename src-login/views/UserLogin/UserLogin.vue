<script setup lang="ts">
import { ref } from 'vue'

// Form state
const loading = ref(false)

// WeChat login handler
const handleWeChatLogin = () => {
  loading.value = true
  // Simulate WeChat login
  // setTimeout(() => {
  //   loading.value = false
  //   alert('微信登录功能待实现')
  // }, 1000)
}

// Other login methods
const handlePhoneLogin = () => {
  alert('手机号登录')
}

const handleEmailLogin = () => {
  alert('邮箱登录')
}

const handleQRLogin = () => {
  alert('扫码登录')
}
</script>

<template>
  <div class="min-h-screen flex flex-col items-center justify-center bg-gray-100 p-4 md:p-10 px-4 md:px-5">
    <div class="bg-white rounded-lg shadow-lg p-8 md:p-15 px-6 md:px-10 w-full max-w-sm md:max-w-100 text-center login-content">
      <!-- Logo -->
      <div class="mb-4 md:mb-6">
        <div class="inline-block">
          <div class="w-14 h-14 md:w-16 md:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-3 flex items-center justify-center text-white mx-auto">
            <div class="i-u-rabbit  text-10"  />
          </div>
        </div>
      </div>

      <!-- 标题 -->
      <div class="mb-6 md:mb-8">
        <h1 class="text-xl md:text-2xl font-semibold text-gray-800 mb-1 md:mb-2">欢迎登录</h1>
        <p class="text-sm md:text-base text-gray-500">请使用微信扫码登录</p>
      </div>

      <!-- 微信登录按钮 -->
      <div class="mb-5 md:mb-6">
        <t-button
          theme="success"
          size="large"
          block
          :loading="loading"
          @click="handleWeChatLogin">
          <template #icon>
            <span class="i-u-wechat"></span>
          </template>
          微信登录
        </t-button>
      </div>

      <!-- Divider -->
      <div class="my-5 md:my-6">
        <t-divider class="divider-custom">其他登录方式</t-divider>
      </div>

      <!-- Footer -->
      <div class="mb-4 md:mb-5">
        <p class="text-xs md:text-sm text-gray-500 leading-relaxed px-2">
          登录即表示同意
          <t-link theme="primary">用户协议</t-link>
          和
          <t-link theme="primary">隐私政策</t-link>
        </p>
      </div>
    </div>

    <!-- Bottom Info -->
    <div class="mt-6 md:mt-10 text-center px-4">
      <p class="text-xs md:text-sm text-gray-400 my-0.5 md:my-1">© {{ new Date().getFullYear() }} 桐乡市濮院兔灵软件设计工作室版权所有</p>
    </div>
  </div>
</template>

<style scoped>
/* 自定义分割线样式 */
.divider-custom :deep(.t-divider) {
  margin: 0;
  color: #bfbfbf;
  font-size: 11px;
}
</style>
